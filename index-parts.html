<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二手车检测报告系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .navbar { background: #007bff; color: white; padding: 1rem 0; margin-bottom: 2rem; }
        .navbar h1 { font-size: 1.5rem; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; overflow: hidden; }
        .card-header { background: #007bff; color: white; padding: 1rem; font-weight: bold; }
        .card-body { padding: 2rem; }
        .upload-area { border: 2px dashed #007bff; border-radius: 8px; padding: 2rem; text-align: center; background: #f8f9fa; cursor: pointer; transition: all 0.3s; margin-bottom: 1rem; }
        .upload-area:hover { border-color: #0056b3; background: #e3f2fd; }
        .upload-area.dragover { border-color: #28a745; background: #d4edda; }
        .upload-area.uploaded { border-color: #28a745; background: #d4edda; }
        .btn { display: inline-block; padding: 0.75rem 1.5rem; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; font-size: 1rem; transition: all 0.3s; }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; }
        .btn-success:disabled { background: #6c757d; cursor: not-allowed; }
        .btn-sm { padding: 0.5rem 1rem; font-size: 0.875rem; }
        .detection-card { border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; margin: 1rem 0; text-align: center; transition: all 0.3s; }
        .detection-card:hover { box-shadow: 0 4px 8px rgba(0,0,0,0.1); transform: translateY(-2px); }
        .detection-card.selected { border-color: #007bff; background: #f0f8ff; }
        .feature-icon { font-size: 3rem; margin-bottom: 1rem; }
        .progress { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-bar { height: 100%; background: #007bff; transition: width 0.3s; }
        .progress-container { display: none; margin: 2rem 0; }
        .report-section { display: none; margin-top: 2rem; opacity: 0; transform: translateY(20px); transition: all 0.5s ease-in-out; }
        .report-section.show { opacity: 1; transform: translateY(0); }
        .report-loading { display: none; text-align: center; padding: 3rem; background: white; border-radius: 10px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); margin: 2rem 0; }
        .report-loading .loading-spinner { width: 60px; height: 60px; border: 4px solid #f3f3f3; border-top: 4px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 1rem; }
        .row { display: flex; flex-wrap: wrap; margin: -0.5rem; }
        .col-md-6 { flex: 0 0 50%; padding: 0.5rem; }
        .col-md-3 { flex: 0 0 25%; padding: 0.5rem; }
        .col-md-4 { flex: 0 0 33.333%; padding: 0.5rem; }
        .col-12 { flex: 0 0 100%; padding: 0.5rem; }
        .text-center { text-align: center; }
        .text-muted { color: #6c757d; }
        .mt-3 { margin-top: 1rem; }
        .mt-4 { margin-top: 1.5rem; }
        .mb-3 { margin-bottom: 1rem; }
        .mb-4 { margin-bottom: 1.5rem; }
        .mb-5 { margin-bottom: 3rem; }
        .alert { padding: 1rem; border-radius: 4px; margin-bottom: 1rem; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .d-flex { display: flex; }
        .justify-content-between { justify-content: space-between; }
        .align-items-center { align-items: center; }
        .table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
        .table th, .table td { padding: 0.75rem; border-bottom: 1px solid #dee2e6; text-align: left; }
        .table th { background: #f8f9fa; font-weight: bold; }
        .table-striped tbody tr:nth-child(odd) { background: #f8f9fa; }
        .badge { display: inline-block; padding: 0.25rem 0.5rem; font-size: 0.75rem; font-weight: bold; border-radius: 0.25rem; }
        .badge-success { background: #28a745; color: white; }
        .border-bottom { border-bottom: 2px solid #dee2e6; padding-bottom: 0.5rem; }
        .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4rem 0; text-align: center; margin-bottom: 3rem; }
        .hero h1 { font-size: 2.5rem; margin-bottom: 1rem; }
        .hero p { font-size: 1.2rem; }
        .loading { animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .loading-hint { background: linear-gradient(90deg, #007bff, #0056b3, #007bff); background-size: 200% 100%; animation: shimmer 2s ease-in-out infinite; padding: 0.75rem 1.5rem; border-radius: 25px; border: 2px solid #007bff; font-weight: bold; box-shadow: 0 2px 10px rgba(0,123,255,0.3); }
        @keyframes shimmer { 0% { background-position: -200% 0; } 100% { background-position: 200% 0; } }
        .image-preview { display: flex; align-items: center; padding: 1rem; border: 1px solid #dee2e6; border-radius: 8px; margin-bottom: 0.5rem; background: white; }
        .image-preview img { width: 80px; height: 80px; object-fit: cover; border-radius: 8px; margin-right: 1rem; border: 2px solid #e9ecef; }
        .image-info { flex: 1; }
        .image-info h6 { margin: 0 0 0.25rem 0; font-weight: bold; }
        .image-info small { color: #6c757d; }
        .part-upload-section { border: 1px solid #e9ecef; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; }
        .part-upload-section.completed { border-color: #28a745; background: #f8fff9; }
        .detection-step { padding: 1rem; border-radius: 8px; background: #f8f9fa; margin: 0.5rem 0; transition: all 0.3s; opacity: 0.5; }
        .detection-step.active { background: #e3f2fd; border: 2px solid #007bff; opacity: 1; transform: scale(1.05); }
        .detection-step.completed { background: #d4edda; border: 2px solid #28a745; opacity: 1; }
        @media (max-width: 768px) {
            .col-md-6, .col-md-3, .col-md-4 { flex: 0 0 100%; }
            .hero h1 { font-size: 2rem; }
            .hero p { font-size: 1rem; }
        }
        input[type="file"] { display: none; }
        input[type="checkbox"] { margin-right: 0.5rem; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <h1>🚗 二手车检测系统</h1>
        </div>
    </nav>

    <!-- 主标题区域 -->
    <div class="hero">
        <div class="container">
            <h1>专业二手车检测报告系统</h1>
            <p>基于AI技术的车辆拍照和三电系统智能检测</p>
        </div>
    </div>

    <div class="container">
        <!-- 上传检测区域 -->
        <section id="upload" class="mb-5">
            <div class="card">
                <div class="card-header">
                    📷 车辆部位图片上传
                </div>
                <div class="card-body">
                    <!-- 分部位图片上传区域 -->
                    <div class="parts-upload-container">
                        <h4 class="mb-4">📷 请按顺序上传各部位图片 <span style="color: red;">*必填</span></h4>
                        
                        <!-- 驱动电机 -->
                        <div class="part-upload-section" id="motorSection">
                            <h5>🔧 1. 驱动电机</h5>
                            <div class="upload-area" id="motorUploadArea" data-part="motor">
                                <div style="font-size: 2em; margin-bottom: 0.5rem;">🔧</div>
                                <h6>上传驱动电机图片</h6>
                                <p class="text-muted">请拍摄驱动电机整体及铭牌</p>
                                <input type="file" id="motorInput" accept=".jpg,.jpeg,.png" capture="environment">
                                <button class="btn btn-primary btn-sm" onclick="document.getElementById('motorInput').click()">
                                    📷 选择图片
                                </button>
                            </div>
                            <div id="motorPreview" class="mt-2"></div>
                        </div>

                        <!-- 电机控制器 -->
                        <div class="part-upload-section" id="controllerSection">
                            <h5>🎛️ 2. 电机控制器</h5>
                            <div class="upload-area" id="controllerUploadArea" data-part="controller">
                                <div style="font-size: 2em; margin-bottom: 0.5rem;">🎛️</div>
                                <h6>上传电机控制器图片</h6>
                                <p class="text-muted">请拍摄控制器外观及接线</p>
                                <input type="file" id="controllerInput" accept=".jpg,.jpeg,.png" capture="environment">
                                <button class="btn btn-primary btn-sm" onclick="document.getElementById('controllerInput').click()">
                                    📷 选择图片
                                </button>
                            </div>
                            <div id="controllerPreview" class="mt-2"></div>
                        </div>

                        <!-- 底盘电池组 -->
                        <div class="part-upload-section" id="batterySection">
                            <h5>🔋 3. 底盘电池组</h5>
                            <div class="upload-area" id="batteryUploadArea" data-part="battery">
                                <div style="font-size: 2em; margin-bottom: 0.5rem;">🔋</div>
                                <h6>上传底盘电池组图片</h6>
                                <p class="text-muted">请拍摄电池组外观及接口</p>
                                <input type="file" id="batteryInput" accept=".jpg,.jpeg,.png" capture="environment">
                                <button class="btn btn-primary btn-sm" onclick="document.getElementById('batteryInput').click()">
                                    📷 选择图片
                                </button>
                            </div>
                            <div id="batteryPreview" class="mt-2"></div>
                        </div>

                        <!-- 上传状态提示 -->
                        <div class="upload-status mt-3">
                            <div class="alert alert-warning" id="uploadAlert">
                                <h6>📋 上传进度</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <span id="motorStatus" class="badge" style="background: #6c757d; color: white;">驱动电机: 未上传</span>
                                    </div>
                                    <div class="col-md-4">
                                        <span id="controllerStatus" class="badge" style="background: #6c757d; color: white;">电机控制器: 未上传</span>
                                    </div>
                                    <div class="col-md-4">
                                        <span id="batteryStatus" class="badge" style="background: #6c757d; color: white;">底盘电池组: 未上传</span>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small>⚠️ 请上传所有三个部位的图片才能开始检测</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 检测项目选择 -->
                    <div class="mt-4">
                        <h4>📋 选择检测项目</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="detection-card" data-type="photo">
                                    <div class="feature-icon">📷</div>
                                    <h4>车辆拍照检测</h4>
                                    <p class="text-muted">VIN码识别、部件完整性、法律合规性检测</p>
                                    <label>
                                        <input type="checkbox" id="photoCheck" value="photo">
                                        选择此项目
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="detection-card" data-type="electric">
                                    <div class="feature-icon">🔋</div>
                                    <h4>三电系统检测</h4>
                                    <p class="text-muted">电池、电机、电控系统全面检测</p>
                                    <label>
                                        <input type="checkbox" id="electricCheck" value="electric">
                                        选择此项目
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 开始检测按钮 -->
                    <div class="text-center mt-4">
                        <button class="btn btn-success" id="startDetection" disabled>
                            <span id="btnIcon">▶️</span>
                            <span id="btnText">开始检测</span>
                        </button>
                        <div id="loadingHint" class="loading-hint" style="display: none; margin-top: 1rem; color: white;">
                            <div class="loading" style="display: inline-block; margin-right: 0.5rem;">⚙️</div>
                            正在启动AI检测引擎，请稍候...
                        </div>
                    </div>

                    <!-- 检测进度 -->
                    <div class="progress-container" id="progressContainer">
                        <div class="text-center mb-4">
                            <div style="font-size: 4em; margin-bottom: 1rem;">
                                <span class="loading">🔍</span>
                            </div>
                            <h3><span class="loading">⚙️</span> AI智能检测进行中</h3>
                            <p class="text-muted">正在分析您上传的车辆部位图片，请耐心等待...</p>
                        </div>
                        <div class="progress mb-3" style="height: 25px;">
                            <div class="progress-bar" id="progressBar" style="width: 0%; font-size: 14px; line-height: 25px;"></div>
                        </div>
                        <div id="progressText" class="text-center" style="font-size: 1.1em; font-weight: bold; color: #007bff;">准备开始检测...</div>

                        <!-- 检测步骤提示 -->
                        <div class="mt-4">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="detection-step" id="step1">
                                        <div style="font-size: 2em;">🔧</div>
                                        <small>驱动电机分析</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="detection-step" id="step2">
                                        <div style="font-size: 2em;">🎛️</div>
                                        <small>控制器检测</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="detection-step" id="step3">
                                        <div style="font-size: 2em;">🔋</div>
                                        <small>电池组诊断</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="detection-step" id="step4">
                                        <div style="font-size: 2em;">📊</div>
                                        <small>报告生成</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 报告生成加载 -->
                    <div class="report-loading" id="reportLoading">
                        <div class="loading-spinner"></div>
                        <h4>🤖 AI正在生成检测报告</h4>
                        <p class="text-muted">正在分析检测数据，生成专业报告...</p>
                        <div class="mt-3">
                            <small class="text-muted">
                                <span class="loading">⚡</span> 数据处理中...
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 检测报告区域 -->
        <section id="report" class="report-section">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    📄 检测报告
                    <button class="btn btn-sm" style="background: #f8f9fa; color: #333; border: 1px solid #dee2e6;" id="exportReport">
                        ⬇️ 导出报告
                    </button>
                </div>
                <div class="card-body">
                    <!-- 报告概览 -->
                    <div class="row mb-4">
                        <div class="col-md-3 text-center">
                            <div class="card">
                                <div class="card-body">
                                    <h5>检测车辆</h5>
                                    <p id="vehicleInfo">比亚迪汉EV 2020款</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="card">
                                <div class="card-body">
                                    <h5>检测时间</h5>
                                    <p id="detectionTime">2025年6月3日</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="card">
                                <div class="card-body">
                                    <h5>检测项目</h5>
                                    <p id="detectionItems">拍照+三电</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="card">
                                <div class="card-body">
                                    <h5>综合评分</h5>
                                    <p id="overallScore">96分</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 车辆拍照检测报告 -->
                    <div id="photoReport" class="mb-5">
                        <h3 class="border-bottom mb-4">📷 车辆拍照检测报告</h3>

                        <!-- 基本信息 -->
                        <table class="table table-striped mb-4">
                            <thead>
                                <tr><th colspan="2">检测基本信息</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>小组编号</td><td>第3组</td></tr>
                                <tr><td>检测车辆</td><td>比亚迪汉EV 2020款（VIN：LC0CD6AB123456789）</td></tr>
                                <tr><td>拍摄设备</td><td>华为P40 Pro（自动模式，焦距10cm）</td></tr>
                                <tr><td>拍摄时间</td><td>2025年6月3日 13:45</td></tr>
                                <tr><td>法律依据</td><td>《民事诉讼法》第63条、《消费者权益保护法》第8条</td></tr>
                            </tbody>
                        </table>

                        <!-- 评分汇总 -->
                        <div class="row mb-4">
                            <div class="col-md-4 text-center">
                                <div class="card">
                                    <div class="card-body">
                                        <h5>VIN码检测</h5>
                                        <span class="badge badge-success" style="font-size: 1.2em; padding: 8px 16px;">100分</span>
                                        <p class="mt-3">优秀</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="card">
                                    <div class="card-body">
                                        <h5>部件完整性</h5>
                                        <span class="badge badge-success" style="font-size: 1.2em; padding: 8px 16px;">60分</span>
                                        <p class="mt-3">优秀</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="card">
                                    <div class="card-body">
                                        <h5>法律风险</h5>
                                        <span class="badge badge-success" style="font-size: 1.2em; padding: 8px 16px;">100分</span>
                                        <p class="mt-3">优秀</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 检测结论 -->
                        <div class="alert alert-success">
                            <h5>✅ 检测结论</h5>
                            <p><strong>综合得分：95分（优秀）</strong></p>
                            <p><strong>操作亮点：</strong></p>
                            <ul>
                                <li>VIN码拍摄位置精准匹配黄金分割位，OCR识别零误差</li>
                                <li>电池包接口细节清晰，可直接用于瑕疵判定</li>
                                <li>所有照片自带时间戳，符合法律证据要求</li>
                            </ul>
                            <p><strong>待改进点：</strong>底盘拍摄角度稍低（离地间隙显示不足），建议使用举升机或垫高拍摄</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 页脚 -->
    <footer style="background: #333; color: white; text-align: center; padding: 2rem 0; margin-top: 3rem;">
        <div class="container">
            <p>&copy; 2025 二手车检测报告系统. 基于AI技术的专业检测平台</p>
        </div>
    </footer>

    <script>
        // 全局变量
        let uploadedParts = {
            motor: null,
            controller: null,
            battery: null
        };
        let selectedDetectionTypes = [];

        // DOM 元素
        const motorInput = document.getElementById('motorInput');
        const controllerInput = document.getElementById('controllerInput');
        const batteryInput = document.getElementById('batteryInput');
        const motorPreview = document.getElementById('motorPreview');
        const controllerPreview = document.getElementById('controllerPreview');
        const batteryPreview = document.getElementById('batteryPreview');
        const motorStatus = document.getElementById('motorStatus');
        const controllerStatus = document.getElementById('controllerStatus');
        const batteryStatus = document.getElementById('batteryStatus');
        const uploadAlert = document.getElementById('uploadAlert');
        const photoCheck = document.getElementById('photoCheck');
        const electricCheck = document.getElementById('electricCheck');
        const startDetectionBtn = document.getElementById('startDetection');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const reportLoading = document.getElementById('reportLoading');
        const reportSection = document.getElementById('report');
        const photoReport = document.getElementById('photoReport');
        const electricReport = document.getElementById('electricReport');

        // 图片上传处理
        motorInput.addEventListener('change', (e) => handlePartUpload(e, 'motor'));
        controllerInput.addEventListener('change', (e) => handlePartUpload(e, 'controller'));
        batteryInput.addEventListener('change', (e) => handlePartUpload(e, 'battery'));

        function handlePartUpload(event, partType) {
            const file = event.target.files[0];
            if (!file) return;

            if (validateFile(file)) {
                uploadedParts[partType] = file;
                displayPartPreview(file, partType);
                updatePartStatus(partType, true);
                updateStartButton();
            }
        }

        function validateFile(file) {
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
            const maxSize = 5 * 1024 * 1024; // 5MB

            if (!allowedTypes.includes(file.type)) {
                alert('不支持的图片格式，请上传 JPG 或 PNG 图片');
                return false;
            }

            if (file.size > maxSize) {
                alert('图片大小超过 5MB 限制');
                return false;
            }

            return true;
        }

        function displayPartPreview(file, partType) {
            const previewContainer = document.getElementById(partType + 'Preview');
            const reader = new FileReader();

            reader.onload = function(e) {
                previewContainer.innerHTML = `
                    <div class="image-preview">
                        <img src="${e.target.result}" alt="${partType}图片预览">
                        <div class="image-info">
                            <h6>${file.name}</h6>
                            <small>大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</small><br>
                            <small>类型: ${file.type}</small>
                        </div>
                        <button class="btn btn-sm" style="background: #dc3545; color: white;" onclick="removePart('${partType}')">
                            ❌ 删除
                        </button>
                    </div>
                `;
            };
            reader.readAsDataURL(file);

            // 更新上传区域样式
            const uploadArea = document.getElementById(partType + 'UploadArea');
            const section = document.getElementById(partType + 'Section');
            uploadArea.classList.add('uploaded');
            section.classList.add('completed');
        }

        function removePart(partType) {
            uploadedParts[partType] = null;
            document.getElementById(partType + 'Preview').innerHTML = '';
            document.getElementById(partType + 'Input').value = '';
            updatePartStatus(partType, false);
            updateStartButton();

            // 恢复上传区域样式
            const uploadArea = document.getElementById(partType + 'UploadArea');
            const section = document.getElementById(partType + 'Section');
            uploadArea.classList.remove('uploaded');
            section.classList.remove('completed');
        }

        function updatePartStatus(partType, uploaded) {
            const statusElement = document.getElementById(partType + 'Status');
            const partNames = {
                motor: '驱动电机',
                controller: '电机控制器',
                battery: '底盘电池组'
            };

            if (uploaded) {
                statusElement.style.background = '#28a745';
                statusElement.textContent = partNames[partType] + ': ✅ 已上传';
            } else {
                statusElement.style.background = '#6c757d';
                statusElement.textContent = partNames[partType] + ': 未上传';
            }

            // 检查是否所有部位都已上传
            const allUploaded = Object.values(uploadedParts).every(part => part !== null);
            if (allUploaded) {
                uploadAlert.className = 'alert alert-success';
                uploadAlert.querySelector('small').textContent = '✅ 所有部位图片已上传完成，可以开始检测';
            } else {
                uploadAlert.className = 'alert alert-warning';
                uploadAlert.querySelector('small').textContent = '⚠️ 请上传所有三个部位的图片才能开始检测';
            }
        }

        // 检测项目选择处理
        photoCheck.addEventListener('change', updateDetectionTypes);
        electricCheck.addEventListener('change', updateDetectionTypes);

        function updateDetectionTypes() {
            selectedDetectionTypes = [];
            if (photoCheck.checked) {
                selectedDetectionTypes.push('photo');
                document.querySelector('[data-type="photo"]').classList.add('selected');
            } else {
                document.querySelector('[data-type="photo"]').classList.remove('selected');
            }

            if (electricCheck.checked) {
                selectedDetectionTypes.push('electric');
                document.querySelector('[data-type="electric"]').classList.add('selected');
            } else {
                document.querySelector('[data-type="electric"]').classList.remove('selected');
            }

            updateStartButton();
        }

        function updateStartButton() {
            const allPartsUploaded = Object.values(uploadedParts).every(part => part !== null);
            const hasDetectionTypes = selectedDetectionTypes.length > 0;
            startDetectionBtn.disabled = !(allPartsUploaded && hasDetectionTypes);
        }

        // 开始检测
        startDetectionBtn.addEventListener('click', startDetection);

        function startDetection() {
            // 立即显示加载状态
            showLoadingState();

            // 短暂延迟后开始检测流程
            setTimeout(() => {
                // 隐藏加载提示，但保持上传区域可见直到进度条显示
                document.getElementById('loadingHint').style.display = 'none';

                // 显示进度条
                progressContainer.style.display = 'block';

                // 开始检测动画
                simulateDetection();

                // 在进度条开始后再隐藏上传区域
                setTimeout(() => {
                    document.getElementById('upload').style.display = 'none';
                }, 500);
            }, 1500);
        }

        function showLoadingState() {
            const btnIcon = document.getElementById('btnIcon');
            const btnText = document.getElementById('btnText');
            const loadingHint = document.getElementById('loadingHint');

            btnIcon.textContent = '⏳';
            btnIcon.classList.add('loading');
            btnText.textContent = '正在启动检测...';
            startDetectionBtn.disabled = true;
            startDetectionBtn.style.background = '#6c757d';
            startDetectionBtn.style.cursor = 'not-allowed';

            loadingHint.style.display = 'block';
        }

        function simulateDetection() {
            const steps = [
                { progress: 15, text: '正在分析驱动电机图片...', stepId: 'step1' },
                { progress: 30, text: '正在分析电机控制器图片...', stepId: 'step2' },
                { progress: 45, text: '正在分析底盘电池组图片...', stepId: 'step3' },
                { progress: 60, text: '正在进行VIN码识别...', stepId: null },
                { progress: 75, text: '正在进行三电系统诊断...', stepId: null },
                { progress: 90, text: '正在生成检测报告...', stepId: 'step4' },
                { progress: 100, text: '检测完成！', stepId: null }
            ];

            let currentStep = 0;

            const interval = setInterval(() => {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];

                    // 更新进度条
                    progressBar.style.width = step.progress + '%';
                    progressBar.textContent = step.progress + '%';
                    progressText.textContent = step.text;

                    // 高亮当前步骤
                    if (step.stepId) {
                        // 清除之前的高亮
                        document.querySelectorAll('.detection-step').forEach(el => {
                            el.classList.remove('active');
                        });

                        // 高亮当前步骤
                        const currentStepEl = document.getElementById(step.stepId);
                        if (currentStepEl) {
                            currentStepEl.classList.add('active');
                        }

                        // 标记之前的步骤为完成
                        if (currentStep > 0) {
                            const prevSteps = steps.slice(0, currentStep).filter(s => s.stepId);
                            prevSteps.forEach(prevStep => {
                                const prevStepEl = document.getElementById(prevStep.stepId);
                                if (prevStepEl) {
                                    prevStepEl.classList.remove('active');
                                    prevStepEl.classList.add('completed');
                                }
                            });
                        }
                    }

                    currentStep++;
                } else {
                    // 标记所有步骤为完成
                    document.querySelectorAll('.detection-step').forEach(el => {
                        el.classList.remove('active');
                        el.classList.add('completed');
                    });

                    clearInterval(interval);
                    setTimeout(showReportLoading, 500);
                }
            }, 1000);
        }

        function showReportLoading() {
            progressContainer.style.display = 'none';
            reportLoading.style.display = 'block';
            setTimeout(showReport, 2000);
        }

        function showReport() {
            reportLoading.style.display = 'none';
            reportSection.style.display = 'block';

            setTimeout(() => {
                reportSection.classList.add('show');
            }, 50);

            // 根据选择的检测类型显示相应报告
            if (selectedDetectionTypes.includes('photo')) {
                photoReport.style.display = 'block';
            } else {
                photoReport.style.display = 'none';
            }

            if (selectedDetectionTypes.includes('electric')) {
                electricReport.style.display = 'block';
            } else {
                electricReport.style.display = 'none';
            }

            updateReportOverview();
            reportSection.scrollIntoView({ behavior: 'smooth' });
        }

        function updateReportOverview() {
            const now = new Date();
            document.getElementById('detectionTime').textContent =
                now.toLocaleDateString('zh-CN') + ' ' + now.toLocaleTimeString('zh-CN');

            let items = [];
            if (selectedDetectionTypes.includes('photo')) items.push('拍照检测');
            if (selectedDetectionTypes.includes('electric')) items.push('三电检测');
            document.getElementById('detectionItems').textContent = items.join('+');

            let totalScore = 0;
            let count = 0;
            if (selectedDetectionTypes.includes('photo')) {
                totalScore += 95;
                count++;
            }
            if (selectedDetectionTypes.includes('electric')) {
                totalScore += 92;
                count++;
            }
            const avgScore = count > 0 ? Math.round(totalScore / count) : 0;
            document.getElementById('overallScore').textContent = avgScore + '分';
        }

        // 导出报告功能
        document.getElementById('exportReport').addEventListener('click', exportReport);

        function exportReport() {
            const reportContent = document.querySelector('#report .card-body').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>二手车检测报告</title>
                    <style>
                        body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
                        th { background: #f5f5f5; }
                        .badge { background: #28a745; color: white; padding: 2px 8px; border-radius: 4px; }
                        .alert { padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 20px 0; }
                        @media print { .btn { display: none; } }
                    </style>
                </head>
                <body>
                    <h1 style="text-align: center;">二手车检测报告</h1>
                    ${reportContent}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('二手车检测系统已加载完成');
        });
    </script>
</body>
</html>
