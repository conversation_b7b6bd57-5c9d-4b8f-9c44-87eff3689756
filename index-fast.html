<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二手车检测报告系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .navbar { background: #007bff; color: white; padding: 1rem 0; margin-bottom: 2rem; }
        .navbar h1 { font-size: 1.5rem; }
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 2rem; overflow: hidden; }
        .card-header { background: #007bff; color: white; padding: 1rem; font-weight: bold; }
        .card-body { padding: 2rem; }
        .upload-area { border: 2px dashed #007bff; border-radius: 8px; padding: 3rem; text-align: center; background: #f8f9fa; cursor: pointer; transition: all 0.3s; }
        .upload-area:hover { border-color: #0056b3; background: #e3f2fd; }
        .upload-area.dragover { border-color: #28a745; background: #d4edda; }
        .btn { display: inline-block; padding: 0.75rem 1.5rem; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; font-size: 1rem; transition: all 0.3s; }
        .btn-primary { background: #007bff; color: white; }
        .btn-primary:hover { background: #0056b3; }
        .btn-success { background: #28a745; color: white; }
        .btn-success:hover { background: #1e7e34; }
        .btn-success:disabled { background: #6c757d; cursor: not-allowed; }
        .btn-light { background: #f8f9fa; color: #333; border: 1px solid #dee2e6; }
        .btn-sm { padding: 0.5rem 1rem; font-size: 0.875rem; }
        .detection-card { border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; margin: 1rem 0; text-align: center; transition: all 0.3s; }
        .detection-card:hover { box-shadow: 0 4px 8px rgba(0,0,0,0.1); transform: translateY(-2px); }
        .detection-card.selected { border-color: #007bff; background: #f0f8ff; }
        .feature-icon { font-size: 3rem; margin-bottom: 1rem; }
        .progress { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-bar { height: 100%; background: #007bff; transition: width 0.3s; }
        .progress-container { display: none; margin: 2rem 0; }
        .report-section {
            display: none;
            margin-top: 2rem;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease-in-out;
        }
        .report-section.show {
            opacity: 1;
            transform: translateY(0);
        }
        /* 报告生成加载状态 */
        .report-loading {
            display: none;
            text-align: center;
            padding: 3rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin: 2rem 0;
        }
        .report-loading .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        .row { display: flex; flex-wrap: wrap; margin: -0.5rem; }
        .col-md-6 { flex: 0 0 50%; padding: 0.5rem; }
        .col-md-3 { flex: 0 0 25%; padding: 0.5rem; }
        .col-md-4 { flex: 0 0 33.333%; padding: 0.5rem; }
        .col-12 { flex: 0 0 100%; padding: 0.5rem; }
        .text-center { text-align: center; }
        .text-muted { color: #6c757d; }
        .mt-3 { margin-top: 1rem; }
        .mt-4 { margin-top: 1.5rem; }
        .mb-3 { margin-bottom: 1rem; }
        .mb-4 { margin-bottom: 1.5rem; }
        .mb-5 { margin-bottom: 3rem; }
        .alert { padding: 1rem; border-radius: 4px; margin-bottom: 1rem; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .d-flex { display: flex; }
        .justify-content-between { justify-content: space-between; }
        .align-items-center { align-items: center; }
        .table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
        .table th, .table td { padding: 0.75rem; border-bottom: 1px solid #dee2e6; text-align: left; }
        .table th { background: #f8f9fa; font-weight: bold; }
        .table-striped tbody tr:nth-child(odd) { background: #f8f9fa; }
        .badge { display: inline-block; padding: 0.25rem 0.5rem; font-size: 0.75rem; font-weight: bold; border-radius: 0.25rem; }
        .badge-success { background: #28a745; color: white; }
        .border-bottom { border-bottom: 2px solid #dee2e6; padding-bottom: 0.5rem; }
        .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4rem 0; text-align: center; margin-bottom: 3rem; }
        .hero h1 { font-size: 2.5rem; margin-bottom: 1rem; }
        .hero p { font-size: 1.2rem; }
        .loading { animation: spin 1s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        /* 加载提示样式 */
        .loading-hint {
            background: linear-gradient(90deg, #007bff, #0056b3, #007bff);
            background-size: 200% 100%;
            animation: shimmer 2s ease-in-out infinite;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            border: 2px solid #007bff;
            font-weight: bold;
            box-shadow: 0 2px 10px rgba(0,123,255,0.3);
        }
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        /* 图片预览样式 */
        .image-preview {
            display: flex;
            align-items: center;
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            background: white;
        }
        .image-preview img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 1rem;
            border: 2px solid #e9ecef;
        }
        .image-info {
            flex: 1;
        }
        .image-info h6 {
            margin: 0 0 0.25rem 0;
            font-weight: bold;
        }
        .image-info small {
            color: #6c757d;
        }
        @media (max-width: 768px) {
            .col-md-6, .col-md-3, .col-md-4 { flex: 0 0 100%; }
            .hero h1 { font-size: 2rem; }
            .hero p { font-size: 1rem; }
        }
        input[type="file"] { display: none; }
        input[type="checkbox"] { margin-right: 0.5rem; }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container">
            <h1>🚗 二手车检测系统</h1>
        </div>
    </nav>

    <!-- 主标题区域 -->
    <div class="hero">
        <div class="container">
            <h1>专业二手车检测报告系统</h1>
            <p>基于AI技术的车辆拍照和三电系统智能检测</p>
        </div>
    </div>

    <div class="container">
        <!-- 上传检测区域 -->
        <section id="upload" class="mb-5">
            <div class="card">
                <div class="card-header">
                    � 车辆图片上传
                </div>
                <div class="card-body">
                    <!-- 图片上传区域 -->
                    <div class="upload-area" id="uploadArea">
                        <div style="font-size: 3em; margin-bottom: 1rem;">�</div>
                        <h3>拖拽图片到此处或点击上传</h3>
                        <p class="text-muted">支持 JPG、PNG 格式，最大 5MB</p>
                        <input type="file" id="fileInput" multiple accept=".jpg,.jpeg,.png" capture="environment">
                        <button class="btn btn-primary mt-3" id="selectImageBtn">
                            � 选择图片
                        </button>
                    </div>

                    <!-- 已上传图片列表 -->
                    <div id="fileList" class="mt-3"></div>

                    <!-- 检测项目选择 -->
                    <div class="mt-4">
                        <h4>📋 选择检测项目</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="detection-card" data-type="photo">
                                    <div class="feature-icon">📷</div>
                                    <h4>车辆拍照检测</h4>
                                    <p class="text-muted">VIN码识别、部件完整性、法律合规性检测</p>
                                    <label>
                                        <input type="checkbox" id="photoCheck" value="photo">
                                        选择此项目
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="detection-card" data-type="electric">
                                    <div class="feature-icon">🔋</div>
                                    <h4>三电系统检测</h4>
                                    <p class="text-muted">电池、电机、电控系统全面检测</p>
                                    <label>
                                        <input type="checkbox" id="electricCheck" value="electric">
                                        选择此项目
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 开始检测按钮 -->
                    <div class="text-center mt-4">
                        <button class="btn btn-success" id="startDetection" disabled>
                            <span id="btnIcon">▶️</span>
                            <span id="btnText">开始检测</span>
                        </button>
                        <div id="loadingHint" class="loading-hint" style="display: none; margin-top: 1rem; color: white;">
                            <div class="loading" style="display: inline-block; margin-right: 0.5rem;">⚙️</div>
                            正在启动AI检测引擎，请稍候...
                        </div>
                    </div>

                    <!-- 检测进度 -->
                    <div class="progress-container" id="progressContainer">
                        <h4><span class="loading">⚙️</span> 检测进行中...</h4>
                        <div class="progress mb-3">
                            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                        </div>
                        <div id="progressText" class="text-center text-muted">准备开始检测...</div>
                    </div>

                    <!-- 报告生成加载 -->
                    <div class="report-loading" id="reportLoading">
                        <div class="loading-spinner"></div>
                        <h4>🤖 AI正在生成检测报告</h4>
                        <p class="text-muted">正在分析检测数据，生成专业报告...</p>
                        <div class="mt-3">
                            <small class="text-muted">
                                <span class="loading">⚡</span> 数据处理中...
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 检测报告区域 -->
        <section id="report" class="report-section">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    📄 检测报告
                    <button class="btn btn-light btn-sm" id="exportReport">
                        ⬇️ 导出报告
                    </button>
                </div>
                <div class="card-body">
                    <!-- 报告概览 -->
                    <div class="row mb-4">
                        <div class="col-md-3 text-center">
                            <div class="card">
                                <div class="card-body">
                                    <h5>检测车辆</h5>
                                    <p id="vehicleInfo">比亚迪汉EV 2020款</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="card">
                                <div class="card-body">
                                    <h5>检测时间</h5>
                                    <p id="detectionTime">2025年6月3日</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="card">
                                <div class="card-body">
                                    <h5>检测项目</h5>
                                    <p id="detectionItems">拍照+三电</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="card">
                                <div class="card-body">
                                    <h5>综合评分</h5>
                                    <p id="overallScore">96分</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 车辆拍照检测报告 -->
                    <div id="photoReport" class="mb-5">
                        <h3 class="border-bottom mb-4">📷 车辆拍照检测报告</h3>
                        
                        <!-- 基本信息 -->
                        <table class="table table-striped mb-4">
                            <thead>
                                <tr><th colspan="2">检测基本信息</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>小组编号</td><td>第3组</td></tr>
                                <tr><td>检测车辆</td><td>比亚迪汉EV 2020款（VIN：LC0CD6AB123456789）</td></tr>
                                <tr><td>拍摄设备</td><td>华为P40 Pro（自动模式，焦距10cm）</td></tr>
                                <tr><td>拍摄时间</td><td>2025年6月3日 13:45</td></tr>
                                <tr><td>法律依据</td><td>《民事诉讼法》第63条、《消费者权益保护法》第8条</td></tr>
                            </tbody>
                        </table>

                        <!-- 评分汇总 -->
                        <div class="row mb-4">
                            <div class="col-md-4 text-center">
                                <div class="card">
                                    <div class="card-body">
                                        <h5>VIN码检测</h5>
                                        <span class="badge badge-success" style="font-size: 1.2em; padding: 8px 16px;">100分</span>
                                        <p class="mt-3">优秀</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="card">
                                    <div class="card-body">
                                        <h5>部件完整性</h5>
                                        <span class="badge badge-success" style="font-size: 1.2em; padding: 8px 16px;">60分</span>
                                        <p class="mt-3">优秀</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="card">
                                    <div class="card-body">
                                        <h5>法律风险</h5>
                                        <span class="badge badge-success" style="font-size: 1.2em; padding: 8px 16px;">100分</span>
                                        <p class="mt-3">优秀</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 检测结论 -->
                        <div class="alert alert-success">
                            <h5>✅ 检测结论</h5>
                            <p><strong>综合得分：95分（优秀）</strong></p>
                            <p><strong>操作亮点：</strong></p>
                            <ul>
                                <li>VIN码拍摄位置精准匹配黄金分割位，OCR识别零误差</li>
                                <li>电池包接口细节清晰，可直接用于瑕疵判定</li>
                                <li>所有照片自带时间戳，符合法律证据要求</li>
                            </ul>
                            <p><strong>待改进点：</strong>底盘拍摄角度稍低（离地间隙显示不足），建议使用举升机或垫高拍摄</p>
                        </div>
                    </div>

                    <!-- 三电系统检测报告 -->
                    <div id="electricReport" class="mb-5">
                        <h3 class="border-bottom mb-4">🔋 三电系统检测报告</h3>

                        <!-- 基本信息 -->
                        <table class="table table-striped mb-4">
                            <thead>
                                <tr><th colspan="2">检测基本信息</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>小组编号</td><td>第3组</td></tr>
                                <tr><td>检测车辆</td><td>比亚迪汉EV 2020款（电池容量76.9kWh）</td></tr>
                                <tr><td>诊断设备</td><td>元征X431 EV专用诊断仪</td></tr>
                                <tr><td>检测时间</td><td>2025年6月3日 15:10</td></tr>
                                <tr><td>系统类型</td><td>电池系统+电机系统+电控系统</td></tr>
                            </tbody>
                        </table>

                        <!-- 系统健康度评估 -->
                        <div class="row mb-4">
                            <div class="col-md-3 text-center">
                                <div class="card">
                                    <div class="card-body">
                                        <h5>电池系统</h5>
                                        <span class="badge badge-success" style="font-size: 1.2em; padding: 8px 16px;">85分</span>
                                        <p class="mt-3">优秀</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="card">
                                    <div class="card-body">
                                        <h5>电机系统</h5>
                                        <span class="badge badge-success" style="font-size: 1.2em; padding: 8px 16px;">92分</span>
                                        <p class="mt-3">优秀</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="card">
                                    <div class="card-body">
                                        <h5>电控系统</h5>
                                        <span class="badge badge-success" style="font-size: 1.2em; padding: 8px 16px;">95分</span>
                                        <p class="mt-3">优秀</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="card">
                                    <div class="card-body">
                                        <h5>综合健康度</h5>
                                        <span class="badge badge-success" style="font-size: 1.2em; padding: 8px 16px;">92分</span>
                                        <p class="mt-3">优秀</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 核心参数检测结果 -->
                        <table class="table table-striped mb-4">
                            <thead>
                                <tr>
                                    <th>检测项目</th>
                                    <th>标准范围</th>
                                    <th>实测值</th>
                                    <th>合规性</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="background: #e3f2fd;"><td colspan="4"><strong>电池系统</strong></td></tr>
                                <tr><td>SOC（荷电状态）</td><td>80-100%</td><td>85%</td><td><span class="badge badge-success">合格</span></td></tr>
                                <tr><td>SOH（健康状态）</td><td>70-100%</td><td>85%</td><td><span class="badge badge-success">优秀</span></td></tr>
                                <tr><td>单体电压差</td><td>≤0.1V</td><td>0.08V</td><td><span class="badge badge-success">优秀</span></td></tr>
                                <tr><td>电池温度</td><td>20-45℃</td><td>32℃</td><td><span class="badge badge-success">合格</span></td></tr>
                                <tr style="background: #f0f8ff;"><td colspan="4"><strong>电机系统</strong></td></tr>
                                <tr><td>电机温度</td><td>25-80℃</td><td>45℃</td><td><span class="badge badge-success">合格</span></td></tr>
                                <tr><td>电机电压</td><td>280-380V</td><td>345V</td><td><span class="badge badge-success">优秀</span></td></tr>
                                <tr><td>电机电流</td><td>≤200A</td><td>180A</td><td><span class="badge badge-success">优秀</span></td></tr>
                                <tr style="background: #fff3cd;"><td colspan="4"><strong>电控系统</strong></td></tr>
                                <tr><td>控制器温度</td><td>25-75℃</td><td>40℃</td><td><span class="badge badge-success">合格</span></td></tr>
                                <tr><td>DC-DC电压</td><td>13.5-14.5V</td><td>14.2V</td><td><span class="badge badge-success">优秀</span></td></tr>
                                <tr><td>故障码</td><td>无高等级故障码</td><td>无（P0000）</td><td><span class="badge badge-success">优秀</span></td></tr>
                            </tbody>
                        </table>

                        <!-- 检测结论 -->
                        <div class="alert alert-success">
                            <h5>✅ 检测结论</h5>
                            <p><strong>综合健康度：92分（优秀）</strong></p>
                            <p><strong>检测亮点：</strong></p>
                            <ul>
                                <li>电池SOH达85%，优于同类车平均水平（78%）</li>
                                <li>单体电压差0.08V，表明电池一致性良好</li>
                                <li>全系统无故障码，数据流平稳无异常</li>
                            </ul>
                            <p><strong>潜在风险：</strong>电池温度32℃接近夏季高温阈值，建议增加散热系统检查频率</p>
                            <p><strong>商业价值：</strong>三电系统健康度优秀，可提升二手车估值5-8%</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 页脚 -->
    <footer style="background: #333; color: white; text-align: center; padding: 2rem 0; margin-top: 3rem;">
        <div class="container">
            <p>&copy; 2025 二手车检测报告系统. 基于AI技术的专业检测平台</p>
        </div>
    </footer>

    <script>
        // 全局变量
        let uploadedFiles = [];
        let selectedDetectionTypes = [];

        // DOM 元素
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');
        const selectImageBtn = document.getElementById('selectImageBtn');
        const photoCheck = document.getElementById('photoCheck');
        const electricCheck = document.getElementById('electricCheck');
        const startDetectionBtn = document.getElementById('startDetection');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const reportLoading = document.getElementById('reportLoading');
        const reportSection = document.getElementById('report');
        const photoReport = document.getElementById('photoReport');
        const electricReport = document.getElementById('electricReport');

        // 图片上传处理
        uploadArea.addEventListener('click', handleUploadAreaClick);
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('drop', handleDrop);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        selectImageBtn.addEventListener('click', handleSelectButtonClick);
        fileInput.addEventListener('change', handleFileSelect);

        function handleUploadAreaClick(e) {
            // 只有点击上传区域本身时才触发，避免按钮重复触发
            if (e.target === uploadArea || e.target.closest('#selectImageBtn') === null) {
                fileInput.click();
            }
        }

        function handleSelectButtonClick(e) {
            e.stopPropagation(); // 阻止事件冒泡
            fileInput.click();
        }

        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            processFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            processFiles(files);
        }

        function processFiles(files) {
            files.forEach(file => {
                if (validateFile(file)) {
                    uploadedFiles.push(file);
                    displayFile(file);
                }
            });
            updateStartButton();
        }

        function validateFile(file) {
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
            const maxSize = 5 * 1024 * 1024; // 5MB

            if (!allowedTypes.includes(file.type)) {
                alert('不支持的图片格式，请上传 JPG 或 PNG 图片');
                return false;
            }

            if (file.size > maxSize) {
                alert('图片大小超过 5MB 限制');
                return false;
            }

            return true;
        }

        function displayFile(file) {
            const fileDiv = document.createElement('div');
            fileDiv.className = 'image-preview';

            // 创建图片预览
            const reader = new FileReader();
            reader.onload = function(e) {
                fileDiv.innerHTML = `
                    <img src="${e.target.result}" alt="图片预览">
                    <div class="image-info">
                        <h6>${file.name}</h6>
                        <small>大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</small><br>
                        <small>类型: ${file.type}</small>
                    </div>
                    <button class="btn btn-sm" style="background: #dc3545; color: white;" onclick="removeFile('${file.name}')">
                        ❌ 删除
                    </button>
                `;
            };
            reader.readAsDataURL(file);

            fileList.appendChild(fileDiv);
        }

        function removeFile(fileName) {
            uploadedFiles = uploadedFiles.filter(file => file.name !== fileName);
            updateFileList();
            updateStartButton();
        }

        function updateFileList() {
            fileList.innerHTML = '';
            uploadedFiles.forEach(file => displayFile(file));
        }

        // 检测项目选择处理
        photoCheck.addEventListener('change', updateDetectionTypes);
        electricCheck.addEventListener('change', updateDetectionTypes);

        function updateDetectionTypes() {
            selectedDetectionTypes = [];
            if (photoCheck.checked) {
                selectedDetectionTypes.push('photo');
                document.querySelector('[data-type="photo"]').classList.add('selected');
            } else {
                document.querySelector('[data-type="photo"]').classList.remove('selected');
            }

            if (electricCheck.checked) {
                selectedDetectionTypes.push('electric');
                document.querySelector('[data-type="electric"]').classList.add('selected');
            } else {
                document.querySelector('[data-type="electric"]').classList.remove('selected');
            }

            updateStartButton();
        }

        function updateStartButton() {
            const hasFiles = uploadedFiles.length > 0;
            const hasDetectionTypes = selectedDetectionTypes.length > 0;
            startDetectionBtn.disabled = !(hasFiles && hasDetectionTypes);
        }

        // 开始检测
        startDetectionBtn.addEventListener('click', startDetection);

        function startDetection() {
            // 立即显示加载状态
            showLoadingState();

            // 短暂延迟后开始检测流程，确保用户看到加载状态
            setTimeout(() => {
                // 隐藏加载提示和上传区域，显示进度条
                document.getElementById('loadingHint').style.display = 'none';
                document.getElementById('upload').style.display = 'none';
                progressContainer.style.display = 'block';

                // 模拟检测过程
                simulateDetection();
            }, 1500); // 增加延迟时间，让用户看到加载状态
        }

        function showLoadingState() {
            // 立即更改按钮状态
            const btnIcon = document.getElementById('btnIcon');
            const btnText = document.getElementById('btnText');
            const loadingHint = document.getElementById('loadingHint');

            btnIcon.textContent = '⏳';
            btnIcon.classList.add('loading');
            btnText.textContent = '正在启动检测...';
            startDetectionBtn.disabled = true;
            startDetectionBtn.style.background = '#6c757d';
            startDetectionBtn.style.cursor = 'not-allowed';

            // 显示加载提示
            loadingHint.style.display = 'block';
        }

        function simulateDetection() {
            const steps = [
                { progress: 15, text: '正在分析上传图片...' },
                { progress: 30, text: '正在进行VIN码识别...' },
                { progress: 50, text: '正在检测车辆部件完整性...' },
                { progress: 70, text: '正在进行三电系统诊断...' },
                { progress: 90, text: '正在生成检测报告...' },
                { progress: 100, text: '检测完成！' }
            ];

            let currentStep = 0;

            const interval = setInterval(() => {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    progressBar.style.width = step.progress + '%';
                    progressText.textContent = step.text;
                    currentStep++;
                } else {
                    clearInterval(interval);
                    // 显示报告生成加载状态
                    setTimeout(showReportLoading, 500);
                }
            }, 800);
        }

        function showReportLoading() {
            // 隐藏进度条，显示报告生成加载
            progressContainer.style.display = 'none';
            reportLoading.style.display = 'block';

            // 2秒后显示最终报告
            setTimeout(showReport, 2000);
        }

        function showReport() {
            // 隐藏报告生成加载，显示报告
            reportLoading.style.display = 'none';
            reportSection.style.display = 'block';

            // 添加平滑显示动画
            setTimeout(() => {
                reportSection.classList.add('show');
            }, 50);

            // 根据选择的检测类型显示相应报告
            if (selectedDetectionTypes.includes('photo')) {
                photoReport.style.display = 'block';
            } else {
                photoReport.style.display = 'none';
            }

            if (selectedDetectionTypes.includes('electric')) {
                electricReport.style.display = 'block';
            } else {
                electricReport.style.display = 'none';
            }

            // 更新报告概览信息
            updateReportOverview();

            // 滚动到报告区域
            reportSection.scrollIntoView({ behavior: 'smooth' });
        }

        function updateReportOverview() {
            const now = new Date();
            document.getElementById('detectionTime').textContent =
                now.toLocaleDateString('zh-CN') + ' ' + now.toLocaleTimeString('zh-CN');

            let items = [];
            if (selectedDetectionTypes.includes('photo')) items.push('拍照检测');
            if (selectedDetectionTypes.includes('electric')) items.push('三电检测');
            document.getElementById('detectionItems').textContent = items.join('+');

            // 计算综合评分
            let totalScore = 0;
            let count = 0;
            if (selectedDetectionTypes.includes('photo')) {
                totalScore += 95;
                count++;
            }
            if (selectedDetectionTypes.includes('electric')) {
                totalScore += 92;
                count++;
            }
            const avgScore = count > 0 ? Math.round(totalScore / count) : 0;
            document.getElementById('overallScore').textContent = avgScore + '分';
        }

        // 导出报告功能
        document.getElementById('exportReport').addEventListener('click', exportReport);

        function exportReport() {
            const reportContent = document.querySelector('#report .card-body').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>二手车检测报告</title>
                    <style>
                        body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
                        th { background: #f5f5f5; }
                        .badge { background: #28a745; color: white; padding: 2px 8px; border-radius: 4px; }
                        .alert { padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; margin: 20px 0; }
                        @media print { .btn { display: none; } }
                    </style>
                </head>
                <body>
                    <h1 style="text-align: center;">二手车检测报告</h1>
                    ${reportContent}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('二手车检测系统已加载完成');
        });
    </script>
</body>
</html>
