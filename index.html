<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二手车检测报告系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0056b3;
            background-color: #e3f2fd;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .detection-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        .detection-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .detection-card.selected {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        .progress-container {
            display: none;
            margin: 20px 0;
        }
        .report-section {
            display: none;
            margin-top: 30px;
        }
        .score-badge {
            font-size: 1.2em;
            padding: 8px 16px;
        }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            font-weight: bold;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        .feature-icon {
            font-size: 3em;
            color: #007bff;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-car"></i> 二手车检测系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#upload">检测上传</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#report">检测报告</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主标题区域 -->
    <div class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 mb-4">专业二手车检测报告系统</h1>
            <p class="lead">基于AI技术的车辆拍照和三电系统智能检测</p>
        </div>
    </div>

    <div class="container">
        <!-- 上传检测区域 -->
        <section id="upload" class="mb-5">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-upload"></i> 车辆检测上传
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- 文件上传区域 -->
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                <h4>拖拽文件到此处或点击上传</h4>
                                <p class="text-muted">支持 JPG、PNG、PDF 格式，最大 10MB</p>
                                <input type="file" id="fileInput" multiple accept=".jpg,.jpeg,.png,.pdf" style="display: none;">
                                <button class="btn btn-primary mt-3" onclick="document.getElementById('fileInput').click()">
                                    <i class="fas fa-folder-open"></i> 选择文件
                                </button>
                            </div>

                            <!-- 已上传文件列表 -->
                            <div id="fileList" class="mt-3"></div>

                            <!-- 检测项目选择 -->
                            <div class="mt-4">
                                <h5><i class="fas fa-tasks"></i> 选择检测项目</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="detection-card" data-type="photo">
                                            <div class="text-center">
                                                <i class="fas fa-camera feature-icon"></i>
                                                <h5>车辆拍照检测</h5>
                                                <p class="text-muted">VIN码识别、部件完整性、法律合规性检测</p>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="photoCheck" value="photo">
                                                    <label class="form-check-label" for="photoCheck">选择此项目</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="detection-card" data-type="electric">
                                            <div class="text-center">
                                                <i class="fas fa-battery-full feature-icon"></i>
                                                <h5>三电系统检测</h5>
                                                <p class="text-muted">电池、电机、电控系统全面检测</p>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="electricCheck" value="electric">
                                                    <label class="form-check-label" for="electricCheck">选择此项目</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 开始检测按钮 -->
                            <div class="text-center mt-4">
                                <button class="btn btn-success btn-lg" id="startDetection" disabled>
                                    <i class="fas fa-play"></i> 开始检测
                                </button>
                            </div>

                            <!-- 检测进度 -->
                            <div class="progress-container" id="progressContainer">
                                <h5><i class="fas fa-cog fa-spin"></i> 检测进行中...</h5>
                                <div class="progress mb-3">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         id="progressBar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <div id="progressText" class="text-center text-muted">准备开始检测...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 检测报告区域 -->
        <section id="report" class="report-section">
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-file-alt"></i> 检测报告
                            </h3>
                            <button class="btn btn-light btn-sm" id="exportReport">
                                <i class="fas fa-download"></i> 导出报告
                            </button>
                        </div>
                        <div class="card-body">
                            <!-- 报告概览 -->
                            <div class="row mb-4">
                                <div class="col-md-3 text-center">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body">
                                            <h5>检测车辆</h5>
                                            <p class="mb-0" id="vehicleInfo">比亚迪汉EV 2020款</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="card bg-info text-white">
                                        <div class="card-body">
                                            <h5>检测时间</h5>
                                            <p class="mb-0" id="detectionTime">2025年6月3日</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body">
                                            <h5>检测项目</h5>
                                            <p class="mb-0" id="detectionItems">拍照+三电</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="card bg-success text-white">
                                        <div class="card-body">
                                            <h5>综合评分</h5>
                                            <p class="mb-0" id="overallScore">96分</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 车辆拍照检测报告 -->
                            <div id="photoReport" class="mb-5">
                                <h4 class="border-bottom pb-2 mb-4">
                                    <i class="fas fa-camera text-primary"></i> 车辆拍照检测报告
                                </h4>

                                <!-- 基本信息 -->
                                <div class="table-responsive mb-4">
                                    <table class="table table-striped">
                                        <thead class="table-dark">
                                            <tr>
                                                <th colspan="2">检测基本信息</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr><td>小组编号</td><td>第3组</td></tr>
                                            <tr><td>检测车辆</td><td>比亚迪汉EV 2020款（VIN：LC0CD6AB123456789）</td></tr>
                                            <tr><td>拍摄设备</td><td>华为P40 Pro（自动模式，焦距10cm）</td></tr>
                                            <tr><td>拍摄时间</td><td>2025年6月3日 13:45</td></tr>
                                            <tr><td>法律依据</td><td>《民事诉讼法》第63条、《消费者权益保护法》第8条</td></tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 拍照项目评分明细 -->
                                <div class="table-responsive mb-4">
                                    <table class="table table-striped">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>检测维度</th>
                                                <th>评分项</th>
                                                <th>标准要求</th>
                                                <th>小组表现</th>
                                                <th>得分</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="table-success">
                                                <td rowspan="3"><strong>VIN码检测</strong></td>
                                                <td>清晰度</td>
                                                <td>OCR置信度>90%</td>
                                                <td>识别结果"LC0CD6AB123456789"，置信度98%</td>
                                                <td><span class="badge bg-success">40分</span></td>
                                            </tr>
                                            <tr class="table-success">
                                                <td>位置合规性</td>
                                                <td>位于黄金分割位（0.382区域）</td>
                                                <td>中心点坐标(382, 410)，符合黄金比例</td>
                                                <td><span class="badge bg-success">30分</span></td>
                                            </tr>
                                            <tr class="table-success">
                                                <td>法律关联度</td>
                                                <td>关联《民诉法》证据条款</td>
                                                <td>报告中引用"第63条 证据必须查证属实"</td>
                                                <td><span class="badge bg-success">30分</span></td>
                                            </tr>
                                            <tr class="table-info">
                                                <td rowspan="6"><strong>部件完整性</strong></td>
                                                <td>VIN码区域</td>
                                                <td>完整显示VIN码及周边2cm范围</td>
                                                <td>拍摄范围包含VIN码铭牌全周边</td>
                                                <td><span class="badge bg-success">10分</span></td>
                                            </tr>
                                            <tr class="table-info">
                                                <td>电池包</td>
                                                <td>45°角拍摄且显示接口细节</td>
                                                <td>角度44°，接口螺丝清晰可见</td>
                                                <td><span class="badge bg-success">10分</span></td>
                                            </tr>
                                            <tr class="table-info">
                                                <td>底盘</td>
                                                <td>完整显示悬架及部件</td>
                                                <td>拍摄到前悬架下摆臂及减震器</td>
                                                <td><span class="badge bg-success">10分</span></td>
                                            </tr>
                                            <tr class="table-info">
                                                <td>发动机舱</td>
                                                <td>清晰显示电机控制器</td>
                                                <td>控制器标签"MCU-2020"可识别</td>
                                                <td><span class="badge bg-success">10分</span></td>
                                            </tr>
                                            <tr class="table-info">
                                                <td>内饰</td>
                                                <td>仪表盘及中控屏完整</td>
                                                <td>显示续航里程、电池状态等数据</td>
                                                <td><span class="badge bg-success">10分</span></td>
                                            </tr>
                                            <tr class="table-info">
                                                <td>全车外观</td>
                                                <td>45°前视+侧视+后视完整</td>
                                                <td>包含左前45°、右侧、左后45°视角</td>
                                                <td><span class="badge bg-success">10分</span></td>
                                            </tr>
                                            <tr class="table-warning">
                                                <td rowspan="3"><strong>法律风险</strong></td>
                                                <td>证据链完整性</td>
                                                <td>无关键部件漏拍</td>
                                                <td>6大部位全部拍摄，无遗漏</td>
                                                <td><span class="badge bg-success">30分</span></td>
                                            </tr>
                                            <tr class="table-warning">
                                                <td>条款引用准确性</td>
                                                <td>正确引用相关法律条款</td>
                                                <td>引用《消法》第8条"如实告知义务"</td>
                                                <td><span class="badge bg-success">30分</span></td>
                                            </tr>
                                            <tr class="table-warning">
                                                <td>数据可追溯性</td>
                                                <td>带时间戳及拍摄设备信息</td>
                                                <td>照片EXIF包含拍摄时间和设备型号</td>
                                                <td><span class="badge bg-success">40分</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 评分汇总 -->
                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title">VIN码检测</h5>
                                                <span class="badge bg-success score-badge">100分</span>
                                                <p class="card-text mt-2">优秀</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title">部件完整性</h5>
                                                <span class="badge bg-success score-badge">60分</span>
                                                <p class="card-text mt-2">优秀</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title">法律风险</h5>
                                                <span class="badge bg-success score-badge">100分</span>
                                                <p class="card-text mt-2">优秀</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 检测结论 -->
                                <div class="alert alert-success">
                                    <h5><i class="fas fa-check-circle"></i> 检测结论</h5>
                                    <p><strong>综合得分：95分（优秀）</strong></p>
                                    <p><strong>操作亮点：</strong></p>
                                    <ul>
                                        <li>VIN码拍摄位置精准匹配黄金分割位，OCR识别零误差</li>
                                        <li>电池包接口细节清晰，可直接用于瑕疵判定</li>
                                        <li>所有照片自带时间戳，符合法律证据要求</li>
                                    </ul>
                                    <p><strong>待改进点：</strong>底盘拍摄角度稍低（离地间隙显示不足），建议使用举升机或垫高拍摄</p>
                                </div>
                            </div>

                            <!-- 三电系统检测报告 -->
                            <div id="electricReport" class="mb-5">
                                <h4 class="border-bottom pb-2 mb-4">
                                    <i class="fas fa-battery-full text-warning"></i> 三电系统检测报告
                                </h4>

                                <!-- 基本信息 -->
                                <div class="table-responsive mb-4">
                                    <table class="table table-striped">
                                        <thead class="table-dark">
                                            <tr>
                                                <th colspan="2">检测基本信息</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr><td>小组编号</td><td>第3组</td></tr>
                                            <tr><td>检测车辆</td><td>比亚迪汉EV 2020款（电池容量76.9kWh）</td></tr>
                                            <tr><td>诊断设备</td><td>元征X431 EV专用诊断仪</td></tr>
                                            <tr><td>检测时间</td><td>2025年6月3日 15:10</td></tr>
                                            <tr><td>系统类型</td><td>电池系统+电机系统+电控系统</td></tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 核心参数检测结果 -->
                                <div class="table-responsive mb-4">
                                    <table class="table table-striped">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>检测项目</th>
                                                <th>标准范围</th>
                                                <th>实测值</th>
                                                <th>偏差率</th>
                                                <th>合规性</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="table-primary">
                                                <td colspan="5"><strong>电池系统</strong></td>
                                            </tr>
                                            <tr>
                                                <td>SOC（荷电状态）</td>
                                                <td>80-100%</td>
                                                <td>85%</td>
                                                <td>+6.25%</td>
                                                <td><span class="badge bg-success">合格</span></td>
                                            </tr>
                                            <tr>
                                                <td>SOH（健康状态）</td>
                                                <td>70-100%</td>
                                                <td>85%</td>
                                                <td>+21.4%</td>
                                                <td><span class="badge bg-success">优秀</span></td>
                                            </tr>
                                            <tr>
                                                <td>单体电压差</td>
                                                <td>≤0.1V</td>
                                                <td>0.08V</td>
                                                <td>-20%</td>
                                                <td><span class="badge bg-success">优秀</span></td>
                                            </tr>
                                            <tr>
                                                <td>电池温度</td>
                                                <td>20-45℃</td>
                                                <td>32℃</td>
                                                <td>+48.5%</td>
                                                <td><span class="badge bg-success">合格</span></td>
                                            </tr>
                                            <tr class="table-info">
                                                <td colspan="5"><strong>电机系统</strong></td>
                                            </tr>
                                            <tr>
                                                <td>电机温度</td>
                                                <td>25-80℃</td>
                                                <td>45℃</td>
                                                <td>+50%</td>
                                                <td><span class="badge bg-success">合格</span></td>
                                            </tr>
                                            <tr>
                                                <td>电机电压</td>
                                                <td>280-380V</td>
                                                <td>345V</td>
                                                <td>+15%</td>
                                                <td><span class="badge bg-success">优秀</span></td>
                                            </tr>
                                            <tr>
                                                <td>电机电流</td>
                                                <td>≤200A</td>
                                                <td>180A</td>
                                                <td>-10%</td>
                                                <td><span class="badge bg-success">优秀</span></td>
                                            </tr>
                                            <tr class="table-warning">
                                                <td colspan="5"><strong>电控系统</strong></td>
                                            </tr>
                                            <tr>
                                                <td>控制器温度</td>
                                                <td>25-75℃</td>
                                                <td>40℃</td>
                                                <td>+37.5%</td>
                                                <td><span class="badge bg-success">合格</span></td>
                                            </tr>
                                            <tr>
                                                <td>DC-DC电压</td>
                                                <td>13.5-14.5V</td>
                                                <td>14.2V</td>
                                                <td>+5.1%</td>
                                                <td><span class="badge bg-success">优秀</span></td>
                                            </tr>
                                            <tr>
                                                <td>故障码</td>
                                                <td>无高等级故障码</td>
                                                <td>无（P0000）</td>
                                                <td>-</td>
                                                <td><span class="badge bg-success">优秀</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 系统健康度评估 -->
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title">电池系统</h5>
                                                <span class="badge bg-success score-badge">85分</span>
                                                <p class="card-text mt-2">优秀</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title">电机系统</h5>
                                                <span class="badge bg-success score-badge">92分</span>
                                                <p class="card-text mt-2">优秀</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title">电控系统</h5>
                                                <span class="badge bg-success score-badge">95分</span>
                                                <p class="card-text mt-2">优秀</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center">
                                            <div class="card-body">
                                                <h5 class="card-title">综合健康度</h5>
                                                <span class="badge bg-success score-badge">92分</span>
                                                <p class="card-text mt-2">优秀</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 检测结论 -->
                                <div class="alert alert-success">
                                    <h5><i class="fas fa-check-circle"></i> 检测结论</h5>
                                    <p><strong>综合健康度：92分（优秀）</strong></p>
                                    <p><strong>检测亮点：</strong></p>
                                    <ul>
                                        <li>电池SOH达85%，优于同类车平均水平（78%）</li>
                                        <li>单体电压差0.08V，表明电池一致性良好</li>
                                        <li>全系统无故障码，数据流平稳无异常</li>
                                    </ul>
                                    <p><strong>潜在风险：</strong>电池温度32℃接近夏季高温阈值，建议增加散热系统检查频率</p>
                                    <p><strong>商业价值：</strong>三电系统健康度优秀，可提升二手车估值5-8%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white text-center py-4 mt-5">
        <div class="container">
            <p>&copy; 2025 二手车检测报告系统. 基于AI技术的专业检测平台</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // 全局变量
        let uploadedFiles = [];
        let selectedDetectionTypes = [];

        // DOM 元素
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');
        const photoCheck = document.getElementById('photoCheck');
        const electricCheck = document.getElementById('electricCheck');
        const startDetectionBtn = document.getElementById('startDetection');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const reportSection = document.getElementById('report');
        const photoReport = document.getElementById('photoReport');
        const electricReport = document.getElementById('electricReport');

        // 文件上传处理
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('drop', handleDrop);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        fileInput.addEventListener('change', handleFileSelect);

        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            processFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            processFiles(files);
        }

        function processFiles(files) {
            files.forEach(file => {
                if (validateFile(file)) {
                    uploadedFiles.push(file);
                    displayFile(file);
                }
            });
            updateStartButton();
        }

        function validateFile(file) {
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
            const maxSize = 10 * 1024 * 1024; // 10MB

            if (!allowedTypes.includes(file.type)) {
                alert('不支持的文件格式，请上传 JPG、PNG 或 PDF 文件');
                return false;
            }

            if (file.size > maxSize) {
                alert('文件大小超过 10MB 限制');
                return false;
            }

            return true;
        }

        function displayFile(file) {
            const fileDiv = document.createElement('div');
            fileDiv.className = 'alert alert-info d-flex justify-content-between align-items-center';
            fileDiv.innerHTML = `
                <div>
                    <i class="fas fa-file"></i>
                    <strong>${file.name}</strong>
                    <small class="text-muted">(${(file.size / 1024 / 1024).toFixed(2)} MB)</small>
                </div>
                <button class="btn btn-sm btn-outline-danger" onclick="removeFile('${file.name}')">
                    <i class="fas fa-times"></i>
                </button>
            `;
            fileList.appendChild(fileDiv);
        }

        function removeFile(fileName) {
            uploadedFiles = uploadedFiles.filter(file => file.name !== fileName);
            updateFileList();
            updateStartButton();
        }

        function updateFileList() {
            fileList.innerHTML = '';
            uploadedFiles.forEach(file => displayFile(file));
        }

        // 检测项目选择处理
        photoCheck.addEventListener('change', updateDetectionTypes);
        electricCheck.addEventListener('change', updateDetectionTypes);

        function updateDetectionTypes() {
            selectedDetectionTypes = [];
            if (photoCheck.checked) {
                selectedDetectionTypes.push('photo');
                document.querySelector('[data-type="photo"]').classList.add('selected');
            } else {
                document.querySelector('[data-type="photo"]').classList.remove('selected');
            }

            if (electricCheck.checked) {
                selectedDetectionTypes.push('electric');
                document.querySelector('[data-type="electric"]').classList.add('selected');
            } else {
                document.querySelector('[data-type="electric"]').classList.remove('selected');
            }

            updateStartButton();
        }

        function updateStartButton() {
            const hasFiles = uploadedFiles.length > 0;
            const hasDetectionTypes = selectedDetectionTypes.length > 0;
            startDetectionBtn.disabled = !(hasFiles && hasDetectionTypes);
        }

        // 开始检测
        startDetectionBtn.addEventListener('click', startDetection);

        function startDetection() {
            // 隐藏上传区域，显示进度条
            document.getElementById('upload').style.display = 'none';
            progressContainer.style.display = 'block';

            // 模拟检测过程
            simulateDetection();
        }

        function simulateDetection() {
            const steps = [
                { progress: 10, text: '正在分析上传文件...' },
                { progress: 25, text: '正在进行VIN码识别...' },
                { progress: 40, text: '正在检测车辆部件完整性...' },
                { progress: 55, text: '正在进行三电系统诊断...' },
                { progress: 70, text: '正在分析电池健康状态...' },
                { progress: 85, text: '正在生成检测报告...' },
                { progress: 100, text: '检测完成！' }
            ];

            let currentStep = 0;

            const interval = setInterval(() => {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    progressBar.style.width = step.progress + '%';
                    progressBar.setAttribute('aria-valuenow', step.progress);
                    progressText.textContent = step.text;
                    currentStep++;
                } else {
                    clearInterval(interval);
                    showReport();
                }
            }, 1000);
        }

        function showReport() {
            // 隐藏进度条，显示报告
            progressContainer.style.display = 'none';
            reportSection.style.display = 'block';

            // 根据选择的检测类型显示相应报告
            if (selectedDetectionTypes.includes('photo')) {
                photoReport.style.display = 'block';
            } else {
                photoReport.style.display = 'none';
            }

            if (selectedDetectionTypes.includes('electric')) {
                electricReport.style.display = 'block';
            } else {
                electricReport.style.display = 'none';
            }

            // 更新报告概览信息
            updateReportOverview();

            // 滚动到报告区域
            reportSection.scrollIntoView({ behavior: 'smooth' });
        }

        function updateReportOverview() {
            const now = new Date();
            document.getElementById('detectionTime').textContent =
                now.toLocaleDateString('zh-CN') + ' ' + now.toLocaleTimeString('zh-CN');

            let items = [];
            if (selectedDetectionTypes.includes('photo')) items.push('拍照检测');
            if (selectedDetectionTypes.includes('electric')) items.push('三电检测');
            document.getElementById('detectionItems').textContent = items.join('+');

            // 计算综合评分
            let totalScore = 0;
            let count = 0;
            if (selectedDetectionTypes.includes('photo')) {
                totalScore += 95;
                count++;
            }
            if (selectedDetectionTypes.includes('electric')) {
                totalScore += 92;
                count++;
            }
            const avgScore = count > 0 ? Math.round(totalScore / count) : 0;
            document.getElementById('overallScore').textContent = avgScore + '分';
        }

        // 导出报告功能
        document.getElementById('exportReport').addEventListener('click', exportReport);

        function exportReport() {
            // 简单的导出功能 - 实际项目中可以使用更专业的PDF生成库
            const reportContent = document.querySelector('#report .card-body').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>二手车检测报告</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                    <style>
                        body { font-family: 'Microsoft YaHei', sans-serif; }
                        @media print {
                            .btn { display: none; }
                        }
                    </style>
                </head>
                <body class="container mt-4">
                    <h1 class="text-center mb-4">二手车检测报告</h1>
                    ${reportContent}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // 导航栏平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('二手车检测系统已加载完成');
        });
    </script>
</body>
</html>
